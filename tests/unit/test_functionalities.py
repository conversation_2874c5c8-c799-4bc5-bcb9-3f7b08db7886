from typing import Iterator
import pytest
import json
from unittest.mock import patch, MagicMock, call
import requests
from pandas import Timestamp
from datetime import datetime
import os

from src.integrations.pacto.tools.integration_tools import PactoIntegrationTools
from src.data.data_processor import DataProcessor
from src.extras.util import (
    Colors,
    split_date,
    parse_phone,
    retry,
    generate_curl_command,
    register_log,
    timestamp_formatado,
    monitor_health,
    is_running,
    convert_to_together_function,
    determine_voice,
    identify_voice,
    identify_pendency,
    get_sensitive_data_by_integragtion,
    check_for_sensitive_data,
    classificao_meta_diaria
)

@pytest.fixture
def pacto_integration_tools():
    with patch("src.integrations.pacto.tools.integration_tools.BigQueryData") as mock_bq:
        mock_bq_instance = mock_bq.return_value
        mock_bq_instance.get_pacto_data.return_value = ("username", "password")
        return PactoIntegrationTools("1-empresa")

@pytest.fixture(autouse=True)
def mock_redis_client():
    with patch("src.integrations.pacto.tools.integration_tools.connections.redis_client") as mock_redis:
        yield mock_redis

@patch("src.integrations.pacto.tools.integration_tools.connections")
@patch("src.integrations.pacto.tools.integration_tools.req")
def test_pit_get_url(mock_req, mock_connections, pacto_integration_tools):
    mock_connections.redis_client.get.return_value = None
    mock_req.get.return_value.json.return_value = {
        "content": {
            "serviceUrls": {
                "type_": "http://example.com"
            }
        }
    }
    mock_req.get.return_value.status_code = 200

    url = pacto_integration_tools.get_url("type_", "1")
    assert url == "http://example.com"

@patch("src.integrations.pacto.tools.integration_tools.register_log")  # Corrected patch target
@patch("src.integrations.pacto.tools.integration_tools.connections")
@patch("src.integrations.pacto.tools.integration_tools.req")
def test_pit_get_token_auth(mock_req, mock_connections, mock_register_log, pacto_integration_tools):
    mock_connections.redis_client.get.return_value = None
    mock_req.post.return_value.json.return_value = {
        "content": {
            "token": "test_token",
            "validade": 180
        }
    }
    mock_req.post.return_value.status_code = 200

    token = pacto_integration_tools.get_token_auth("1")
    assert token == "test_token"

    # Optionally, assert that register_log was called correctly
    mock_register_log.assert_called()

@patch("src.integrations.pacto.tools.integration_tools.connections")
@patch("src.integrations.pacto.tools.integration_tools.req")
def test_pit_get_levels(mock_req, mock_connections, pacto_integration_tools):
    mock_connections.redis_client.get.side_effect = [None, b'test_token']
    mock_req.get.return_value.json.return_value = {"data": "levels"}
    mock_req.get.return_value.status_code = 200

    levels = pacto_integration_tools.get_levels()
    assert levels == {"data": "levels"}

@patch("src.integrations.pacto.tools.integration_tools.connections")
@patch("src.integrations.pacto.tools.integration_tools.req")
def test_pit_get_user_by_id(mock_req, mock_connections, pacto_integration_tools):
    mock_connections.redis_client.get.side_effect = [b'http://localhost', b'test_token']
    mock_req.get.return_value.json.return_value = {"result": [{"user": "data"}]}
    mock_req.get.return_value.status_code = 200

    user = pacto_integration_tools.get_user_by_id("user_id")
    assert user == {"user": "data"}

@patch("src.integrations.pacto.tools.integration_tools.Timestamp")  # Mocking register_log
@patch("src.integrations.pacto.tools.integration_tools.register_log")  # Mocking register_log
@patch("src.integrations.pacto.tools.integration_tools.connections")
@patch("src.integrations.pacto.tools.integration_tools.req")
def test_pit_search_by_phone(mock_req, mock_connections, mock_register_log, mock_timestamp, pacto_integration_tools):
    # Define a side_effect function for mock_req.get to handle different URLs
    def mock_get(url, headers=None, params=None, timeout=None):
        mock_response = MagicMock()
        if f"/find/1" in url:
            # Mock response for get_url method
            mock_response.json.return_value = {
                "content": {
                    "serviceUrls": {
                        "admMsUrl": "http://adm-service.com"
                    }
                }
            }
            mock_response.status_code = 200
        elif "/v1/cliente" in url:
            # Mock response for search_by_phone method
            mock_response.json.return_value = {
                "content": [{"codigo": "user_id"}]
            }
            mock_response.status_code = 200
        else:
            # Default mock response for unexpected URLs
            mock_response.json.return_value = {}
            mock_response.status_code = 404
        return mock_response
    
    # Assign the side_effect function to mock_req.get
    mock_req.get.side_effect = mock_get

    # Timestamp.now().timestamp()
    mock_timestamp.now.return_value.timestamp.return_value = 0

    # Mock the get_user_by_id method to return predefined user data
    pacto_integration_tools.get_user_by_id = MagicMock(return_value={"fase_crm": "test_phase", "user": "data"})
    
    # Mock Redis client interactions
    mock_connections.redis_client.get.side_effect = [None, b'test_token']

    # Execute the method under test
    result, data = pacto_integration_tools.search_by_phone("123456789")
    
    # Assertions to verify the behavior
    assert result == True
    assert data == {"fase_crm": "test_phase", "user": "data"}

    # Verify that Redis set was called twice with correct parameters
    expected_calls = [
        call('1-empresa:admMsUrl', 'http://adm-service.com', ex=86400),  # Adjust SECONDS_TO_CACHE as defined
        call('123456789-1-empresa', '{"fase_crm": "test_phase", "user": "data"}', ex=8*60*60)
    ]
    mock_connections.redis_client.set.assert_has_calls(expected_calls, any_order=False)

    # Additionally, you can verify the number of times set was called
    assert mock_connections.redis_client.set.call_count == 2

    # Verify that lpush was called once with the correct parameters
    mock_connections.redis_client.lpush.assert_called_once()
    
    # Optionally, assert that register_log was called correctly
    mock_register_log.assert_called()

def test_process_plans_data():
    # Dados de exemplo para "planosComVendaOnline"
    data = {
        "planosComVendaOnline": [
            {
                "descricao": "Plano Online 1",
                "urlVendaOnline": "http://compra-plano1.com",
                "condicaoPagamento": "À vista",
                "descricaoEncantamento": "Plano premium para você",
                "modalidades": [{"modalidade": "Musculação"}, {"modalidade": "Yoga"}],
                "valorMensal": 100.0,
                "taxaAdesao": 50.0,
                "valorAnuidade": 1200.0,
                "mesAnuidade": "Janeiro",
                "diaAnuidade": "15",
                "anuidadeNaParcela": True,
                "valorTotalDoPlano": 1350.0,
                "duracaoPlano": "12 meses",
                "quantidadeDiasExtra": 5,
                "planoPersonal": True,
                "regimeRecorrencia": True,
                "renovavelAutomaticamente": True,
                "maximoVezesParcelar": 12,
                "categorias": ["Premium", "VIP"]
            }
        ],
        "planosComVendaConsultor": [
            {
                "descricao": "Plano Consultor 1",
                "percentualmultacancelamento": 10,
                "recorrencia": True,
                "permitiracessosomentenaempresavendeucontrato": False,
                "planopersonal": True,
                "permitepagarcomboleto": True,
                "convidadospormes": 2,
                "valordescontoboletopagantecipado": 20.0,
                "porcentagemdescontoboletopagantecipado": 5.0
            }
        ]
    }

    # Chamar o método process_plans_data
    string, nomes_planos = DataProcessor.process_plans_data(data)

    # Verificar a string gerada
    assert "Plano Online 1" in string
    assert "Esse é o link para comprar o plano: http://compra-plano1.com" in string
    assert "Essas são as modalidades do plano Plano Online 1: \nMusculação\nYoga" in string
    assert "Plano Consultor 1" in string
    assert "Esse é o percentual de multa por cancelamento: 10" in string
    assert "Esse plano é recorrente" in string
    assert "Esse plano permite acesso em todas as unidades relacionadas a essa academia" in string
    assert "Esse plano é disponível para personal trainers" in string
    assert "Esse plano permite pagamento com boleto" in string
    assert "Esse plano permite 2 convidados por mês" in string
    assert "Esse é o valor do desconto para pagamento antecipado no boleto: 20.0" in string
    assert "Esse é o percentual do desconto para pagamento antecipado no boleto: 5.0" in string
    assert "CASO O CLIENTE PERGUNTE ALGO DIFERENTE SOBRE O PLANO, RECOMENDE FALAR COM UM CONSULTOR" in string

    # Verificar os nomes dos planos
    assert "Plano Online 1" in nomes_planos
    assert "Plano Consultor 1" in nomes_planos

def test_process_gym_data():
    # Dados de exemplo
    data = {
        "nomeFantasia": "Academia XYZ",
        "razaoSocial": "XYZ Fitness LTDA",
        "endereco": "Rua das Palmeiras, 123",
        "cidade": "São Paulo",
        "estado": "SP",
        "cep": "12345-678",
        "complemento": "Próximo ao Shopping",
        "numero": "456",
        "setor": "Centro",
        "cnpj": "12.345.678/0001-90",
        "site": "www.academiaxyz.com.br",
        "email": "<EMAIL>",
        "telComercial1": "(11) 1234-5678",
        "telComercial2": "(11) 8765-4321",
        "telComercial3": None,
        "permiteContratosConcomitantes": True,
        "urlLojaVendaOnline": "www.academiaxyz.com.br/loja",
        "urlPlanosLojaVendaOnline": "www.academiaxyz.com.br/planos",
        "urlProdutosLojaVendaOnline": "www.academiaxyz.com.br/produtos",
        "urlAgendaAulasLojaVendaOnline": "www.academiaxyz.com.br/agenda",
        "horario_funcionamento": "Segunda a Sexta, das 6h às 22h",
        "proposito": "Oferecer saúde e bem-estar",
        "site_texts": "Visite nosso site para mais informações"
    }

    # Resultado esperado
    expected_output = (
        "Nome da empresa: Academia XYZ\n"
        "Endereço: Rua das Palmeiras, 123\n"
        "Cidade: São Paulo\n"
        "Estado: SP\n"
        "CEP: 12345-678\n"
        "Complemento: Próximo ao Shopping\n"
        "Número: 456\n"
        "Setor: Centro\n"
        "CNPJ: 12.345.678/0001-90\n"
        "Site da empresa: www.academiaxyz.com.br\n"
        "Email: <EMAIL>\n"
        "Telefone comercial 1: (11) 1234-5678\n"
        "Telefone comercial 2: (11) 8765-4321\n"
        "Essa empresa permite contratos concomitantes \n"
        "Esse é o link para a loja de venda online: www.academiaxyz.com.br/loja \n"
        "Esse é o link para os planos na loja de venda online: www.academiaxyz.com.br/planos \n"
        "Esse é o link para os produtos na loja de venda online: www.academiaxyz.com.br/produtos \n"
        "Esse é o link para o aluno ver a agenda das aulas online: www.academiaxyz.com.br/agenda \n"
        "Esse é o horário de funcionamento da academia: Segunda a Sexta, das 6h às 22h\n"
        "Propósito da empresa: Oferecer saúde e bem-estar\n"
        "Informações adicionais do site: Visite nosso site para mais informações\n"
    )

    # Chamar o método a ser testado
    result = DataProcessor.process_gym_data(data)

    # Verificar se a saída corresponde ao esperado
    assert result == expected_output, f"Saída inesperada: {result}"

def test_process_user_data(monkeypatch):
    #NOTE: defini uma classe que retorna uma data fixa.
    # isso foi uma tentativa de solucionar um problema 
    # que dava na hora do teste no pipeline CI/CD ao mergear.
    class MockDateTime(datetime):
        @classmethod
        def now(cls, tz=None):
            return cls(2025, 7, 8)
        
    class MockPIT:
        def __init__(self, id_empresa):
            pass

        def get_payment_link(self, *args, **kwargs):
            return {"link": "http://link.de.pagamento.mockado"}
        
    caminho_do_modulo = "src.data.data_processor"
        
    target_path = f"{__name__}.datetime"
    monkeypatch.setattr(target_path, MockDateTime)
    monkeypatch.setattr(f'{caminho_do_modulo}.PIT', MockPIT)

    # Simula o JSON de entrada
    user_data = {
        "aluno": {
            "pessoa": {
                "nome": "João Silva",
                "dataNasc": "1990-01-01",
                "profissao": "Engenheiro",
                "telefonesconsulta": ["123456789"],
                "emails": [{"email": "<EMAIL>"}, {"email": "<EMAIL>"}]
            },
            "matricula": "12345",
            "codigo": "C12345", 
            "situacao": {
                "descricao": "Ativo",
                "grupo": {"descricao": "Premium"}
            },
            "fase_crm": "Leads"
        },
        "parcelas": [
            # Parcela PAGA: deve ser ignorada
            {
                "descricao": "PARCELA 1", "valorParcela": 100.0,
                "situacao": {"codigo": "PG", "descricao": "Pago"}, "datavencimento": "01/01/2024 00:00:00"
            },
            # Parcela RENEGOCIADA
            {
                "descricao": "PARCELA 2", "valorParcela": 75.0,
                "situacao": {"codigo": "RG", "descricao": "Renegociado"}, "datavencimento": "01/02/2024 00:00:00"
            },
            # Parcela EM ABERTO
            {
                "descricao": "PARCELA 3", "valorParcela": 100.0,
                "situacao": {"codigo": "EA", "descricao": "Em Aberto"}, "datavencimento": "01/03/2024 00:00:00"
            },
            # Parcela CANCELADA
            {
                "descricao": "PARCELA 4", "valorParcela": 100.0,
                "situacao": {"codigo": "CA", "descricao": "Cancelado"}, "datavencimento": "01/04/2024 00:00:00"
            },
            # Parcela PENDENTE: situacao é None (null) e está vencida
            {
                "descricao": "PARCELA 7", "valorParcela": 89.0,
                "situacao": None, "datavencimento": "27/06/2025 00:00:00"
            },
            # Parcela PENDENTE: situacao é None (null) e está vencida
            {
                "descricao": "PARCELA 6", "valorParcela": 89.0,
                "situacao": None, "datavencimento": "27/05/2025 00:00:00"
            }
        ],
        "contratos": [
            {
                "datamatricula": "2023-01-01",
                "vigenciaate": "2024-01-01",
                "vigenciade": "2023-01-01",
                "valorfinal": 1200.0,
                "situacaocontrato": {"descricao": "Ativo"},
                "plano": {"descricao": "Plano Anual"}
            }
        ],
        "historicoContatos": [
            {
                "dia": "2024-01-10",
                "observacao": "Contato para renovação",
                "fase": {"descricao": "Renovação"}
            }
        ],
        "programasTreino": {
            "nomeProgramaAtual": "Treino Funcional",
            "totalAulasPrevistas": 12,
            "nrTreinosRealizados": 8,
            "programas": [{"nome": "Funcional", "descricao": "Treino intensivo"}]
        },
        "avaliacaoFisica": {
            "totalAvaliacoes": 3,
            "periodoDias": 30,
            "diasProximaAvaliacao": 7,
            "dataProxima": "2024-01-15",
            "percentualMassaGorda": 15.0,
            "percentualMassaMagra": 85.0,
            "massaGordaInicial": 20.0,
            "massaGordaAtual": 15.0,
            "evolucaoGeral": "Melhorando",
            "massaMagraInicial": 60.0,
            "massaMagraAtual": 65.0,
            "nivelGorduraCorporal": "Normal",
            "nivelGorduraCorporalInicial": "Acima",
            "nivelGorduraCorporalFaltando": "Reduzir 5%",
            "grupos": [{"nome": "Treino de Força"}]
        },
        "acessos": ["2024-01-01"],
        "boletimDeVisita": {
            "questionarioCliente": {"pergunta1": "resposta1"},
            "questionarioPergunta": {"pergunta2": "resposta2"}
        }
    }

    # Chama a função
    result = DataProcessor.process_user_data(user_data, id_empresa="teste", telefone="12345")

    assert "Nome: João Silva" in result
    assert "Data de nascimento: 1990-01-01" in result
    assert "Matrícula: 12345" in result
    assert "Situação: Ativo" in result
    assert "Histórico de contatos: \n- Dia: 2024-01-10" in result
    assert "Contratos: \n- Data da matrícula: 2023-01-01" in result
    assert "### DÉBITOS PENDENTES ###" in result
    assert "ATENÇÃO: O aluno possui débitos." in result
    expected_due_1 = "- Descrição: PARCELA 7 | Vencimento: 27/06/2025 | Valor: R$ 89,00"
    assert expected_due_1 in result
    expected_due_2 = "- Descrição: PARCELA 6 | Vencimento: 27/05/2025 | Valor: R$ 89,00"
    assert expected_due_2 in result
    assert "Instrução: Para regularizar, envie o seguinte link: http://link.de.pagamento.mockado" in result
    assert "PARCELA 1" not in result

def test_process_message_text():
    data = {"text": "Olá, tudo bem?"}
    expected = ({'type': 'text'}, "Olá, tudo bem?")
    result = DataProcessor.process_message(data)
    assert result == expected

def test_process_message_audio():
    with patch("src.data.data_processor.proccess_audio") as mock_proccess_audio:
        mock_proccess_audio.return_value = ("Texto transcrito", 10)

        data = {"audio": "path/to/audio/file"}
        result = DataProcessor.process_message(data)

        assert result == ({"type": "audio", "n_seconds": 10}, "Texto transcrito")
        mock_proccess_audio.assert_called_once_with("path/to/audio/file")

@pytest.mark.parametrize(
    "data, expected_type, expected_content",
    [
        # Caso de sticker
        ({"sticker": "sticker_content"}, "sticker", "sticker_content"),
        # Caso de image
        ({"image": "image_url"}, "image", "image_url"),
        # Caso de contact
        ({"contact": {"name": "John Doe", "phone": "123456789"}}, "contact", {"name": "John Doe", "phone": "123456789"}),
        # Caso de document
        ({"document": "document_url"}, "contact", "document_url"),
        # Caso de location
        ({"location": {"latitude": 40.7128, "longitude": -74.0060}}, "location", {"latitude": 40.7128, "longitude": -74.0060}),
        # Caso de video
        ({"video": "video_url"}, "video", "video_url"),
        # Caso de poll
        ({"poll": {"question": "What is your favorite color?", "options": ["Red", "Blue"]}}, "poll", {"question": "What is your favorite color?", "options": ["Red", "Blue"]}),
        # Caso de reaction
        ({"reaction": {"value": "👍"}}, "reaction", "👍"),
        # Caso de notification
        ({"notification": "You have a new message"}, "notification", "You have a new message"),
    ]
)
def test_process_message(data, expected_type, expected_content):
    result_type, result_content = DataProcessor.process_message(data)
    
    # Verifique o tipo e o conteúdo retornados
    assert result_type.get("type") == expected_type, f"Esperava tipo {expected_type}, mas recebeu {result_type.get('type')}"
    assert result_content == expected_content, f"Esperava conteúdo {expected_content}, mas recebeu {result_content}"

def test_process_classes_data():
    # Entrada simulada
    data = {
        "content": {
            "dias": ["2024-12-27"],
            "itens": [
                {
                    "horario": "08:00",
                    "dias": {
                        "2024-12-27": [
                            {
                                "codigo": "A123",
                                "turma": "Yoga Iniciante",
                                "capacidade": 20,
                                "ocupacao": 15
                            },
                            {
                                "codigo": "B456",
                                "turma": "Pilates Avançado",
                                "capacidade": 10,
                                "ocupacao": 8
                            }
                        ]
                    }
                },
                {
                    "horario": "10:00",
                    "dias": {
                        "2024-12-27": [
                            {
                                "codigo": "C789",
                                "turma": "Crossfit",
                                "capacidade": 25,
                                "ocupacao": 20
                            }
                        ]
                    }
                }
            ]
        }
    }

    # Resultado esperado
    expected_output = (
        "# Aulas para o dia 2024-12-27 (Informe esse dia para o usuário):\n"
        "## O código da aula é o código que o ASSISTENTE deverá usar para o agendamento da aula.\n"
        "## (*ATENÇÃO, o código só irá funcionar para o dia 2024-12-27, "
        "verifique se bate com o dia que você irá tentar agendar a aula.*)\n"
        "### Essas são as aulas disponíveis ás **08:00**:\n"
        "#### Código da aula: A123\n"
        "- Turma: Yoga Iniciante\n"
        "- Vagas disponíveis: 5\n\n"
        "#### Código da aula: B456\n"
        "- Turma: Pilates Avançado\n"
        "- Vagas disponíveis: 2\n\n"
        "_____________________________________________________\n"
        "### Essas são as aulas disponíveis ás **10:00**:\n"
        "#### Código da aula: C789\n"
        "- Turma: Crossfit\n"
        "- Vagas disponíveis: 5\n\n"
        "_____________________________________________________\n"
    )

    # Executa a função
    output = DataProcessor.process_classes_data(data)

    # Validação
    assert output == expected_output, f"Saída incorreta: {output}"

@pytest.mark.parametrize(
    "input_data, expected_string, expected_fichas",
    [
        # Caso completo com todos os dados
        (
            {
                "name_training_plan": "Plano A",
                "training_plan": {
                    "days_per_week": 3,
                    "workouts": [
                        {
                            "workout_name": "Treino 1",
                            "type_of_training": "Cardio",
                            "duration": 60,
                            "activities": [
                                {
                                    "exercise_name": "Corrida",
                                    "sets": 3,
                                    "repetitions": {"min": 10, "max": 15},
                                    "rest_interval": 30,
                                    "bodyPart": "Pernas",
                                    "target": "Resistência",
                                    "instructions": ["Aqueça antes", "Alongue depois"]
                                }
                            ]
                        }
                    ]
                }
            },
            (
                "*Nome do plano de treino: Plano A*\n"
                "Dias por semana: 3\n"  # Corrigido aqui
                "*Treino 1*\n"
                "- Tipo de treino: Cardio\n"
                "- Duração: 60\n"
                "- Nome do exercício: *Corrida*\n"
                "- Número de séries: 3\n"
                "- Repetições:\n"
                "\t- Mínimo: 10 repetições\n"
                "\t- Máximo: 15 repetições\n"
                "\t- Intervalo de descanso: 30 segundos\n"
                "- Parte do corpo: *Pernas*\n"
                "- Alvo: Resistência\n"
                "- Instruções: \n"
                "\t- Aqueça antes\n"
                "\t- Alongue depois\n"
            ),
            [
                (
                    "*Treino 1*\n"
                    "- Tipo de treino: Cardio\n"
                    "- Duração: 60\n"
                    "- Nome do exercício: *Corrida*\n"
                    "- Número de séries: 3\n"
                    "- Repetições:\n"
                    "\t- Mínimo: 10 repetições\n"
                    "\t- Máximo: 15 repetições\n"
                    "\t- Intervalo de descanso: 30 segundos\n"
                    "- Parte do corpo: *Pernas*\n"
                    "- Alvo: Resistência\n"
                    "- Instruções: \n"
                    "\t- Aqueça antes\n"
                    "\t- Alongue depois\n"
                )
            ]
        ),
        # Caso com dados incompletos
        (
            {
                "name_training_plan": "Plano B",
                "training_plan": {
                    "days_per_week": 2,
                    "workouts": [
                        {
                            "workout_name": "Treino 2",
                            "activities": [
                                {
                                    "exercise_name": "Agachamento",
                                    "sets": 4
                                }
                            ]
                        }
                    ]
                }
            },
            (
                "*Nome do plano de treino: Plano B*\n"
                "Dias por semana: 2\n"  # Corrigido aqui
                "*Treino 2*\n"
                "- Nome do exercício: *Agachamento*\n"
                "- Número de séries: 4\n"
            ),
            [
                (
                    "*Treino 2*\n"
                    "- Nome do exercício: *Agachamento*\n"
                    "- Número de séries: 4\n"
                )
            ]
        ),
        # Caso vazio
        (
            {},
            "",
            []
        ),
    ]
)
def test_process_train_data(input_data, expected_string, expected_fichas):
    # Chama a função process_train_data
    result_string, result_fichas = DataProcessor.process_train_data(input_data)

    # Verifica se a string gerada é igual à esperada
    assert result_string == expected_string, f"Esperava:\n{expected_string}\nRecebeu:\n{result_string}"

    # Verifica se as fichas geradas são iguais às esperadas
    assert result_fichas == expected_fichas, f"Esperava fichas:\n{expected_fichas}\nRecebeu:\n{result_fichas}"

@pytest.mark.parametrize(
    "input_data, expected_output",
    [
        # Caso com produtos completos
        (
            [
                {"descricao": "Produto 1", "valorfinal": 10.5, "urlVendaOnline": "https://compra1.com"},
                {"descricao": "Produto 2", "valorfinal": 20.0, "urlVendaOnline": "https://compra2.com"},
            ],
            (
                "Produto: Produto 1\n"
                "Preço: 10.5\n"
                "Link para compra: R$https://compra1.com\n"
                "_____________________________________________________\n"
                "Produto: Produto 2\n"
                "Preço: 20.0\n"
                "Link para compra: R$https://compra2.com\n"
                "_____________________________________________________\n"
            ),
        ),
        # Caso com dados vazios
        ([], "Não há produtos disponíveis."),
        # Caso com JSON em string
        (
            json.dumps([
                {"descricao": "Produto 3", "valorfinal": 15.0, "urlVendaOnline": "https://compra3.com"}
            ]),
            (
                "Produto: Produto 3\n"
                "Preço: 15.0\n"
                "Link para compra: R$https://compra3.com\n"
                "_____________________________________________________\n"
            ),
        ),
    ]
)
def test_process_products_data(input_data, expected_output):
    result = DataProcessor.process_products_data(input_data)
    assert result == expected_output, f"Esperava:\n{expected_output}\nRecebeu:\n{result}"

@pytest.mark.parametrize(
    "input_text, expected_output",
    [
        # Caso com texto contendo link dentro de colchetes
        (
            "[Texto do link](http://example.com) Informação extra",
            "http://example.com Informação extra",
        ),
        # Caso com texto contendo apenas colchetes
        (
            "[Texto dentro de colchetes] Informação extra",
            "Texto dentro de colchetes Informação extra",
        ),
        # Caso com texto com negrito e hashtags
        (
            "**Texto em negrito** com #hashtag",
            "*Texto em negrito* com #hashtag",
        ),
        (
            "## Título com hashtag",
            "Título com hashtag",
        ),
        # Caso sem nada a ser preprocessado
        (
            "Texto simples sem alterações",
            "Texto simples sem alterações",
        ),
    ]
)
def test_preprocess_text(input_text, expected_output):
    result = DataProcessor.preprocess_text(input_text)
    assert result == expected_output, f"Esperava:\n{expected_output}\nRecebeu:\n{result}"

def test_colors():
    colors = Colors()
    text = "Teste"
    assert colors.colorize_text(text, "green") == f"\033[92m{text}\033[0m"
    assert colors.colorize_text(text, "red") == f"\033[91m{text}\033[0m"

def test_split_date():
    assert split_date("2024-12-31T23:59:59.123") == "2024-12-31T23:59:59"
    assert split_date("2024-12-31T23:59:59+00:00") == "2024-12-31T23:59:59"

def test_parse_phone():
    assert parse_phone("550012345678") == "+5500912345678"
    assert parse_phone("+550012345678") == "+5500912345678"

def test_retry_decorator():
    @retry(retries=2, delay=1)
    def flaky_function():
        raise requests.exceptions.RequestException("Erro simulado")
    
    with pytest.raises(requests.exceptions.RequestException) as exc_info:
        flaky_function()
    
    # Verifica a mensagem de erro da exceção levantada
    assert "Erro simulado" in str(exc_info.value)
    
def test_generate_curl_command():
    url = "http://example.com"
    req_body = {"key": "value"}
    headers = {"Authorization": "Bearer token"}
    command = generate_curl_command(url, req_body, headers)
    assert "curl -X POST" in command
    assert "-H 'Authorization: Bearer token'" in command
    assert "-d '{\"key\": \"value\"}'" in command

@patch("src.extras.util.connections")
def test_register_log(mock_connections):
    mock_redis = MagicMock()
    mock_connections.redis_client = mock_redis
    response_mock = MagicMock()
    response_mock.status_code = 200
    response_mock.json.return_value = {"key": "value"}
    
    register_log(
        url="http://example.com",
        req_body={"key": "value"},
        headers={"Authorization": "Bearer token"},
        method="POST",
        response=response_mock,
        function="test_function",
        id_empresa="123",
    )
    assert mock_redis.rpush.called

def test_timestamp_formatado():
    result = timestamp_formatado()
    assert isinstance(result, str)

@patch("src.extras.util.connections")
def test_monitor_health(mock_connections):
    mock_redis = MagicMock()
    mock_connections.redis_client = mock_redis

    @monitor_health
    def sample_function():
        return "success"

    assert sample_function() == "success"
    assert mock_redis.set.called

@patch("src.extras.util.connections")
def test_is_running(mock_connections):
    mock_redis = MagicMock()
    mock_connections.redis_client = mock_redis
    mock_redis.get.return_value = json.dumps({"last_run": "2024-12-31T23:59:59"})
    assert is_running("test_key") is True

@patch("src.extras.util.convert_to_openai_function")
def test_convert_to_together_function(mock_convert):
    mock_convert.return_value = {"function": "example"}
    result = convert_to_together_function(lambda x: x)
    assert result == {"type": "function", "function": {"function": "example"}}
    mock_convert.assert_called_once()

@patch("src.extras.util.connections")
@patch("src.extras.util.OpenAIUtilModule")
def test_identify_pendency_with_pendency(mock_openai, mock_connections):
    os.environ["CHECK_PENDENCY"] = "true"
    redis_mock = MagicMock()
    mock_connections.redis_client = redis_mock

    # Configurando o mock para retornar uma string JSON válida
    valid_json = json.dumps([
        {"role": "assistant", "content": "Por favor, envie o documento."},
        {"role": "user", "content": "Enviei o documento, mas não obtive resposta."}
    ])
    redis_mock.get.side_effect = lambda key: valid_json if key == "last_messages-+5511999999999-123" else None

    mock_openai_instance = MagicMock()
    mock_openai.return_value = mock_openai_instance
    mock_openai_instance.get_response.return_value = MagicMock(
        type=MagicMock(value="has_pendency")
    )

    # Chamada da função testada
    has_pendency, _ = identify_pendency("123", "+5511999999999", MagicMock())

    # Verificação
    assert has_pendency is True

@patch("src.extras.util.connections")
@patch("src.extras.util.OpenAIUtilModule")
def test_identify_pendency_no_pendency(mock_openai, mock_connections):
    redis_mock = MagicMock()
    mock_connections.redis_client = redis_mock

    # Configurando o mock para retornar uma string JSON válida
    valid_json = json.dumps([
        {"role": "user", "content": "Por favor, envie o documento."},
        {"role": "assistant", "content": "O documento foi enviado pelo seu e-mail!"},
        {"role": "user", "content": "Obrigado!"}
    ])
    redis_mock.get.side_effect = lambda key: valid_json if key == "last_messages-+5511999999-123" else None

    mock_openai_instance = MagicMock()
    mock_openai.return_value = mock_openai_instance
    mock_openai_instance.get_response.return_value = MagicMock(
        type=MagicMock(value="no_pendency")
    )

    # Chamada da função testada
    has_pendency, _ = identify_pendency("123", "+5511999999", MagicMock())

    # Verificação
    assert has_pendency is False

@patch("src.extras.util.OpenAIUtilModule")
def test_identify_voice(mock_openai):
    mock_openai_instance = MagicMock()
    mock_openai.return_value = mock_openai_instance

    # Simulação do LLM retornando um agendamento de voz
    mock_openai_instance.get_response.side_effect = [
        MagicMock(type=MagicMock(value="hourly")),  # Primeiro retorno para `schedule_type`
        {"data": [{"hour": "08:00", "voice": "feminino"}]}  # Segundo retorno para os dados
    ]

    personality = "Personalidade carismática das 13h às 17h, fora isso você é mais séria."
    result = identify_voice(personality, MagicMock())

    # Verificação do resultado
    assert result["schedule_type"] == "hourly"
    assert result["data"] == [{"hour": "08:00", "voice": "feminino"}]
    mock_openai_instance.get_response.assert_called()  # Verifica se o LLM foi chamado

@patch("src.extras.util.connections")
def test_determine_voice(mock_connections):
    redis_mock = MagicMock()
    mock_connections.redis_client = redis_mock

    # Simulação de agendamento no Redis
    schedule = {
        "schedule_type": "hourly",
        "data": [
            {"hour": "08:00", "voice": "feminino"},
            {"hour": "14:00", "voice": "masculino"}
        ]
    }
    redis_mock.get.return_value = json.dumps(schedule)

    # Simulação do horário atual
    with patch("src.extras.util.Timestamp.now") as mock_now:
        mock_now.return_value = Timestamp("2024-12-26 08:15:00")
        voice = determine_voice("123")

    # Verificação do resultado
    assert voice == "nova"  # Voz mais próxima definida no horário

    # Teste para "weekly"
    schedule["schedule_type"] = "weekly"
    schedule["data"] = [
        {"day": "Thursday", "voice": "masculino"},
        {"day": "Friday", "voice": "feminino"}
    ]
    redis_mock.get.return_value = json.dumps(schedule)
    with patch("src.extras.util.Timestamp.now") as mock_now:
        mock_now.return_value = Timestamp("2024-12-26 12:00:00")
        voice = determine_voice("123")
    assert voice == "onyx"

    # Teste para fallback (voz padrão)
    redis_mock.get.return_value = None
    voice = determine_voice("123")
    assert voice == "nova"

@pytest.mark.parametrize(
    "integration, expected_data",
    [
        # Caso Pacto
        ("pacto", {
            "dont_respond": "ignore",
            "end_conversation": "ignore",
            "get_additional_context": "regenerate",
            "warn_user": "regenerate",
            "save_user_level": "regenerate",
            "save_user_birthdate": "regenerate",
            "check_classes_day": "regenerate",
            "check_class_details": "regenerate",
            "book_class": "regenerate",
            "book_call": "regenerate",
            "save_user_name": "regenerate",
            "register_visitor": "regenerate",
            "search_by_cpf": "regenerate",
            "generate_train": "regenerate"
        }),
        # Caso integração desconhecida
        ("unknown", {}),
        # Caso integração None
        (None, {}),
    ]
)
def test_get_sensitive_data_by_integragtion(integration, expected_data):
    assert get_sensitive_data_by_integragtion(integration) == expected_data

@pytest.mark.parametrize(
    "message, integration, expected_result",
    [
        # Caso Pacto com dados sensíveis - regen
        ("Por favor, save_user_name", "pacto", (True, "regenerate")),
        # Caso Pacto com dados sensíveis - ignore
        ("Função chamada: end_conversation", "pacto", (True, "ignore")),
        # Caso Pacto sem dados sensíveis
        ("Olá, como você está?", "pacto", (False, "")),
        # Caso integração desconhecida
        ("Por favor, save_user_name", "unknown", (False, "")),
        # Caso mensagem None
        (None, "pacto", (False, "")),
    ]
)
def test_check_for_sensitive_data(message, integration, expected_result):    
    assert check_for_sensitive_data(message, integration) == expected_result

@pytest.mark.parametrize(
    "data, age, expected_output",
    [
        # Caso: turma disponível (nenhuma é filtrada) -> agora retorna todas como disponíveis.
        (
            [{"descricao": "Yoga", "codigo": "A1", "idademinima": 18, "idademaxima": 65}],
            25,
            "Todas as turmas estão disponíveis.\n_____________________________________________________\n"
        ),
        # Caso: turma não disponível (gera aviso)
        (
            [{"descricao": "Pilates", "codigo": "B1", "idademinima": 30, "idademaxima": 60}],
            25,
            "ATENÇÃO: as seguintes turmas **não estão disponíveis** devido à idade do aluno.\nTurma: Pilates\n_____________________________________________________\n"
        ),
        # Caso: mistura de turmas (apenas a não disponível é listada)
        (
            [
                {"descricao": "Yoga", "codigo": "A1", "idademinima": 18, "idademaxima": 65},
                {"descricao": "Pilates", "codigo": "B1", "idademinima": 30, "idademaxima": 60}
            ],
            25,
            "ATENÇÃO: as seguintes turmas **não estão disponíveis** devido à idade do aluno.\nTurma: Pilates\n_____________________________________________________\n"
        ),
        # Caso: idade 0 (ignora restrições e retorna todas as aulas disponíveis)
        (
            [{"descricao": "Yoga", "codigo": "A1", "idademinima": 18, "idademaxima": 65}],
            0,
            "Todas as turmas estão disponíveis.\n_____________________________________________________\n"
        ),
        # Caso: Idade None (interpreta como 0 e retorna todas as aulas disponíveis)
        (
            [{"descricao": "Yoga", "codigo": "A1", "idademinima": 18, "idademaxima": 65}],
            None,
            "Todas as turmas estão disponíveis.\n_____________________________________________________\n"
        ),
    ]
)
def test_process_available_classes_data(data, age, expected_output):
    """Testa a função process_available_classes_data"""
    result = DataProcessor.process_available_classes_data(data, age)
    assert result == expected_output, f"\nEsperado:\n{expected_output}\nRecebido:\n{result}"

def test_process_static_classes_data_string_output():
    data = [
        {
            "descricao": "Aula de Yoga",
            "mensagem": "Aula relaxante",
            "modalidade": "Físico",
            "horarios": "08:00 - 12:00",
            "dias": "Segunda a Sexta",
            "idademinima": 18,
            "idademaxima": 65,
            "aulacoletiva": True,
            "permitiraulaexperimental": False,
            "permitirdesmarcarreposicoes": True
        }
    ]
    expected = (
        "Aula: Aula de Yoga\n"
        "Mensagem: Aula relaxante\n"
        "Modalidade: Físico\n"
        "Horários: 08:00 - 12:00\n"
        "Dias: Segunda a Sexta\n"
        "Idade mínima: 18\n"
        "Idade máxima: 65\n"
        "Aula coletiva: True\n"
        "Permite aula experimental: False\n"
        "Permite desmarcar reposições: True\n"
        "_____________________________________________________\n"
    )
    result = DataProcessor.process_static_classes_data(data)
    assert result == expected

def test_process_static_classes_data_chunks_output():
    data = [
        {
            "descricao": "Aula de Yoga",
            "mensagem": "Aula relaxante",
            "modalidade": "Físico",
            "horarios": "08:00 - 12:00",
            "dias": "Segunda a Sexta",
            "idademinima": 18,
            "idademaxima": 65,
            "aulacoletiva": True,
            "permitiraulaexperimental": False,
            "permitirdesmarcarreposicoes": True
        }
    ]
    expected_chunk = [(
        "Aula: Aula de Yoga\n"
        "Mensagem: Aula relaxante\n"
        "Modalidade: Físico\n"
        "Horários: 08:00 - 12:00\n"
        "Dias: Segunda a Sexta\n"
        "Idade mínima: 18\n"
        "Idade máxima: 65\n"
        "Aula coletiva: True\n"
        "Permite aula experimental: False\n"
        "Permite desmarcar reposições: True\n"
        "_____________________________________________________\n"
    )]
    result = DataProcessor.process_static_classes_data(data, return_chunks=True)
    assert result == expected_chunk


@patch("src.extras.util.connections")
@patch("src.worker.llm_modules.llm_utils.openai_util_module.Process")
@patch("src.integrations.pacto.tools.integration_tools.connections")
@patch("src.integrations.pacto.tools.integration_tools.req")
@patch("src.integrations.pacto.tools.integration_tools.register_log")
@pytest.mark.parametrize(
    "tipo, codigo_lead, codigo_cliente, acao",
    [
        ("objecao", "123", None, "MORA LONGE"),
        ("objecao", None, "123", "MORA LONGE"),
        ("agendamento", "123", None, "aula_Experimental"),
        ("agendamento", None, "123", "aula_Experimental"),
        ("simples_registro", "123", None, None),
        ("simples_registro", None, "123", None),
    ]
)
def test_classificacao_meta_diaria(
    mock_register_log,
    mock_requests,
    mock_pacto_connections,
    mock_process,
    mock_utils_connections,
    tipo,
    codigo_lead,
    codigo_cliente,
    acao
):
    # Teste 1: Agendamento
        # 1.1: Agendamento de aula
        # 1.2: Agendamento de ligação
    # Teste 2: Objeção
    # Teste 3: Simples registro
    mock_utils_connections.redis_client = MagicMock()
    mock_process.return_value = MagicMock()

    mensagens_por_tipo = {
        "objecao": [
            (0, {
                "enviado_por": "cliente",
                "mensagem": "Estou analisando a proposta, mas o valor está muito acima do que posso pagar."
            }),
            (1, {
                "enviado_por": "atendente",
                "mensagem": "Entendo sua preocupação, mas nossos planos oferecem diversas vantagens."
            }),
            (2, {
                "enviado_por": "cliente",
                "mensagem": "Mesmo assim, não vejo justificativa para esse investimento no momento."
            })
        ],
        "simples_registro": [
            (0, {
                "enviado_por": "sistema",
                "mensagem": "Tentativa de contato realizada, sem resposta do cliente."
            })
        ],
        "agendamento": [
            (0, {
                "enviado_por": "cliente",
                "mensagem": "Gostaria de agendar uma aula experimental para conhecer melhor o serviço."
            }),
            (1, {
                "enviado_por": "atendente",
                "mensagem": "Claro, vamos agendar. Qual dia e horário seria mais conveniente para você?"
            }),
            (2, {
                "enviado_por": "cliente",
                "mensagem": "Prefiro na próxima quarta-feira à tarde."
            })
        ]
    }
    mensagens = mensagens_por_tipo[tipo]
    get_messages_response = MagicMock()
    def mensagens_iterrows():
        for mensagem in mensagens:
            yield mensagem
    get_messages_response.iterrows.return_value = mensagens_iterrows()
    bq_mock = MagicMock()
    bq_mock.get_messages.return_value = get_messages_response

    descriptions_por_tipo = {
        "objecao": [
            "MORA LONGE",
            "ACHOU CARO",
            "NÃO GOSTOU DA ACADEMIA",
            "SÓ VEIO CONHECER",
            "NÃO POSSUI CHEQUE/CARTÃO"
        ],
        "agendamento": [
                "aula_Experimental"
                "ligação",
                "visita"
        ],
        "simples_registro": []
    }
    descriptions = descriptions_por_tipo[tipo]
    pit_mock = MagicMock()
    pit_mock.get_descriptions.return_value = descriptions
    classification, action_description = classificao_meta_diaria(
        telefone="5511999999999",
        bq=bq_mock,
        pit=pit_mock,
        id_conversa="123",
    )
    # classification, action_description = tipo, "MORA LONGE"

    assert classification == tipo

    pit = PactoIntegrationTools(id_empresa="123-1")
    pit.get_url = MagicMock(return_value="http://example.com")
    pit.get_token_auth = MagicMock(return_value="token")

    redis_value_por_tipo = {
        "objecao": {
            "MORA LONGE": 1,
            "ACHOU CARO": 2,
            "NÃO GOSTOU DA ACADEMIA": 3,
            "SÓ VEIO CONHECER": 4,
            "NÃO POSSUI CHEQUE/CARTÃO": 5
        },
        "agendamento": {
            "tipo": "EA",
            "horario": "14:00",
            "data": "2024-12-31",
            "modalidade": "Físico",
            "tipo_professor": "Físico",
            "codigo_professor": "123"
        },
        "simples_registro": None
    }
    redis_value = redis_value_por_tipo[tipo]

    mock_pacto_connections.redis_client.get.return_value = json.dumps(redis_value)

    mock_requests.post.return_value = MagicMock(status_code=200)

    pit.save_meta_diaria(
        id_conversa="123",
        classificacao=classification,
        descricao_acao=action_description,
        fase_atual="Leads",
        codigo_cliente=codigo_cliente,
        codigo_lead=codigo_lead
    )

    mock_register_log.assert_called_once()
