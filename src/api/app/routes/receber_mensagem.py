import logging
import json
from datetime import datetime

from flask import Blueprint, request, jsonify, current_app

from src.api.app.tracing import tracer
from src.api.app.auth.utils.auth_wrappers import verify_origin
from src.api.app.shared.processar_mensagem_recebida import processar_e_rotear_mensagem
from src.data.bigquery_data import get_from_instance
from src.extras.util import RoutesTracing, parse_phone

logger = logging.getLogger("conversas_logger")

receber_mensagem_bp = Blueprint('receber_mensagem', __name__)


def is_tag_change_notification(data: dict) -> bool:
    return data.get("notification", "") == "CHAT_LABEL_ASSOCIATION"


def update_user_tag(data: dict) -> None:
    instance_id = data.get("instanceId")
    id_empresa, _ = get_from_instance(instance_id)
    notification_parameters = data.get("notificationParameters", {})
    for notification_param in notification_parameters:
        label = notification_param.get("label")
        assigned = notification_param.get("assigned")
        phone = parse_phone(notification_param.get('phone'))
        logger.info("[TAG_ZAPI] Atualizando tag para o telefone %s", phone)
        logger.info("[TAG_ZAPI] Empresa: %s", id_empresa)
        logger.info("[TAG_ZAPI] Parâmetros: %s", notification_param)
        logger.info("[TAG_ZAPI] Tag: %s", label)
        logger.info("[TAG_ZAPI] Adicionada: %s", assigned)
        task = {
            "type": "api-v2",
            "id_empresa": id_empresa,
            "data": {
                "phone": phone,
                "tag_id": int(label)
            },
            "action": "save" if assigned else "remove",
            "resource": "tag"
        }
        current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))


@receber_mensagem_bp.route('', methods=['POST'])
@verify_origin()
@RoutesTracing(
    span_name_prefix="receber_mensagem",
    capture_body_fields=["id_empresa"],
    capture_query_params=["id_empresa"],
)
def receber_mensagem(origin="z_api"):
    try:
        momento_recebimento = datetime.now().timestamp()
        logger.info("\n\n\nEntrou no receber mensagem\n\n\n")
        data = request.json

        id_empresa = data.get('chave_empresa', None)

        if not data:
            return jsonify({"error": "No data received"}), 400
        
        if data.get("status") == "SENT":
            return jsonify({"success": "Received"}), 200

        if is_tag_change_notification(data):
            update_user_tag(data)
            return jsonify({"success": "Tag updated"}), 200

        id_empresa = data.get('chave_empresa', None)

        logger.info(f"request: {json.dumps(request.json, indent=2)}")

        if origin == "z_api":
            id_empresa, _ = get_from_instance(data["instanceId"])

        elif origin == "gym_bot":
            id_empresa = request.args["id_empresa"]

        if data.get("messageId", None) == "integration-test":
            id_empresa = request.args["id_empresa"]

        # Task inicial (mínima)
        task_inicial = {
            "id_empresa": id_empresa,
            "data": data,
            "sessionId": None,
            "canAnswer": True,
            "origin": origin,
            "momento_recebimento": momento_recebimento
        }

        response_data, status_code = processar_e_rotear_mensagem(
            task_inicial=task_inicial,
            redis_client=current_app.redis_client,
            logger=logger,
            tracer=tracer
        )

        return jsonify(response_data), status_code

    except Exception as e:
        logger.error(f"Error: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500
