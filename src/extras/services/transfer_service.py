"""Módulo para o serviço de transferência"""

import logging

from src.data.bigquery_data import BigQueryData as BQ
from src.integrations.z_api.tools.integration_tools import ZApiIntegrationTools as ZApi

logger = logging.getLogger(__name__)

def can_answer(
    id_empresa: str, phone: str
):
    can_answer_ = True
    bq = BQ(id_empresa)
    tags = bq.get_tags(phone)
    if tags:
        logger.info(f"[TASK ADAPTER] Tags encontradas:\n%s", tags)
        deparments = bq.get_departments(orient="index", index_column="id_departamento")
        logger.info(f"[TASK ADAPTER] Departamentos encontrados:\n%s", deparments)
        def is_human_tag(tag):
            try:
                details = deparments.get(str(tag)) or {}
                logger.info(f"[TASK ADAPTER] Detalhes do departamento:\n%s", details)
                department_exists = details != {}
                is_human = details.get("name") != "ConversasAI"
                return (
                    department_exists and is_human
                )
            except ValueError:
                return False

        can_answer_ = not any(is_human_tag(tag) for tag in tags)
    return can_answer_

def tag_user(
    id_empresa: str,
    phone: str,
    tag_id: int
) -> bool:
    """
    Adiciona uma tag a um usuário (telefone) em uma empresa.

    Args:
        id_empresa (str): ID da empresa.
        phone (str): Número de telefone do usuário.
        tag_id (int): ID da tag.

    Returns:
        bool: True se a tag foi adicionada com sucesso, False caso contrário.
    """
    zapi = ZApi(id_empresa)
    result = zapi.add_tag_to_phone(
        phone, tag_id
    )
    bq = BQ(id_empresa)
    bq.save_tag(
        {
            "phone": phone,
            "tag_id": tag_id
        }
    )
    logger.info("[TRANSFER_SERVICE] Resultado da adição da tag: %s", result)
    return result


def untag_user(
    id_empresa: str,
    phone: str,
    tag_id: int
) -> bool:
    """
    Remove uma tag de um usuário (telefone) em uma empresa.

    Args:
        id_empresa (str): ID da empresa.
        phone (str): Número de telefone do usuário.
        tag_id (int): ID da tag.

    Returns:
        bool: True se a tag foi removida com sucesso, False caso contrário.
    """
    zapi = ZApi(id_empresa)
    result = zapi.remove_tag_from_phone(
        phone, tag_id
    )
    bq = BQ(id_empresa)
    bq.remove_tag(
        {
            "phone": phone,
            "tag_id": tag_id
        }
    )
    logger.info("[TRANSFER_SERVICE] Resultado da adição da tag: %s", result)
    return result


def make_transfer(
    id_empresa: str,
    phone: str,
    tag_id: int
) -> bool:
    """
    Realiza a transferência de um usuário de um departamento para outro.

    Args:
        id_empresa (str): ID da empresa.
        phone (str): Número de telefone do usuário.
        to_department_id (str): ID do departamento de destino.

    Returns:
        bool: True se a transferência foi bem-sucedida, False caso contrário.
    """
    logger.info(
        "[TRANSFER_SERVICE] Iniciando transferência para phone=%s, empresa=%s, destino=%s",
        phone, id_empresa, tag_id
    )
    bq = BQ(id_empresa)
    tags = bq.get_tags(phone)
    for tag in tags:
        untag_user(id_empresa, phone, tag)

    added = tag_user(id_empresa, phone, tag_id)

    if added:
        message_template = (
            "🌟 Uma nova transferência foi realizada em {date}!\n\n"
            "O usuário {user_info} foi transferido para o departamento {department} com sucesso. "
            "Desejamos que ele(a) tenha um excelente atendimento nesta nova etapa! 🚀"
        )
    else:
        message_template = (
            "⚠️ Uma tentativa de transferência ocorreu em {date}.\n\n"
            "O usuário {user_info} ainda não foi transferido para o departamento {department}. "
            "Por favor, faça a transferência manualmente, alterando a tag dele para \"{department}\". 💬"
        )
    logger.info(
        "[TRANSFER_SERVICE] Tag do departamento de destino adicionada: %s", added
    )
    return added, message_template
